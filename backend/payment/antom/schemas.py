from pydantic import BaseModel, Field, field_validator
from typing import Optional, Dict, Any
import uuid


# Antom 会话创建相关模式
class AntomSessionRequest(BaseModel):
    """Antom会话创建请求"""
    payment_id: str = Field(..., description="本地支付记录ID")
    
    @field_validator('payment_id')
    def validate_payment_id(cls, v):
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError('Invalid payment ID format')
        return v


class AntomSessionResponse(BaseModel):
    """Antom会话创建响应"""
    success: bool = Field(..., description="是否成功")
    payment_id: str = Field(..., description="本地支付记录ID")
    antom_payment_request_id: str = Field(..., description="Antom支付请求ID")
    session_data: Dict[str, Any] = Field(..., description="Antom会话数据")
    redirect_url: Optional[str] = Field(None, description="支付页面重定向URL")


# Antom 回调相关模式
class AntomCallbackRequest(BaseModel):
    """Antom回调请求"""
    notify_type: str = Field(..., description="通知类型")
    payment_request_id: str = Field(..., description="支付请求ID")
    result_code: str = Field(..., description="结果代码")
    result_status: str = Field(..., description="结果状态")
    result_message: str = Field(..., description="结果消息")
    payment_amount: Optional[Dict[str, Any]] = Field(None, description="支付金额信息")
    payment_time: Optional[str] = Field(None, description="支付时间")
    
    class Config:
        extra = "allow"  # 允许额外字段


class AntomCallbackResponse(BaseModel):
    """Antom回调响应"""
    result_code: str = Field(..., description="处理结果代码")
    result_message: str = Field(..., description="处理结果消息")
    result_status: str = Field(..., description="处理结果状态")


# 通知结果模式
class NotifyResult(BaseModel):
    """通知结果"""
    result_code: str = Field(..., alias="resultCode", description="结果代码")
    result_message: str = Field(..., alias="resultMessage", description="结果消息")
    result_status: str = Field(..., alias="resultStatus", description="结果状态")


class NotifyResponse(BaseModel):
    """通知响应"""
    result: NotifyResult = Field(..., description="通知结果")
