import json
import time
from ninja_extra import api_controller, route
from ninja.errors import HttpError
from ninja_jwt.authentication import J<PERSON><PERSON><PERSON>
from django.http import HttpRequest
from django.conf import settings
from django.db import transaction
from django.utils import timezone

from com.alipay.ams.api.default_alipay_client import DefaultAlipayClient
from com.alipay.ams.api.exception.exception import <PERSON><PERSON>yApiException
from com.alipay.ams.api.model.amount import Amount
from com.alipay.ams.api.model.buyer import Buyer
from com.alipay.ams.api.model.goods import Goods
from com.alipay.ams.api.model.order import Order
from com.alipay.ams.api.model.product_code_type import ProductCodeType
from com.alipay.ams.api.request.notify.alipay_capture_result_notify import AlipayCaptureResultNotify
from com.alipay.ams.api.request.notify.alipay_pay_result_notify import AlipayPayResultNotify
from com.alipay.ams.api.request.pay.alipay_create_session_request import AlipayCreateSessionRequest
from com.alipay.ams.api.tools.webhook_tool import check_signature

from ..models import Payment
from .schemas import AntomSessionRequest, AntomSessionResponse, NotifyResult, NotifyResponse
from ..payments.services import PaymentService


@api_controller("/payment/antom", tags=["Antom支付"])
class AntomPaymentController:
    def __init__(self):
        self.default_alipay_client = DefaultAlipayClient(
            settings.ANTOM_GATEWAY_URL,
            settings.ANTOM_CLIENT_ID,
            settings.ANTOM_MERCHANT_PRIVATE_KEY,
            settings.ANTOM_PUBLIC_KEY
        )
        # 记录当前使用的环境
        self.current_environment = getattr(settings, 'ANTOM_ENVIRONMENT', 'sandbox')
        print(f"Antom Payment Controller initialized with environment: {self.current_environment}")

    @route.get("/config", response=dict)
    def get_payment_config(self, request):
        """获取Antom支付配置信息（不包含敏感信息）"""
        return {
            "environment": getattr(settings, 'ANTOM_ENVIRONMENT', 'sandbox'),
            "gateway_url": settings.ANTOM_GATEWAY_URL,
            "client_id_preview": settings.ANTOM_CLIENT_ID[:8] + '...' if settings.ANTOM_CLIENT_ID else 'Not Set',
            "notify_url": settings.ANTOM_NOTIFY_URL,
            "redirect_url": settings.ANTOM_REDIRECT_URL,
            "supported_currencies": ["CNY", "HKD", "USD"],
            "supported_tiers": ["free", "premium", "plus"]
        }

    @route.post("/session", response=AntomSessionResponse, auth=JWTAuth())
    def create_antom_session(self, request, data: AntomSessionRequest):
        """创建Antom支付会话"""
        try:
            # 获取支付记录
            payment = Payment.objects.get(id=data.payment_id, user=request.auth)

            if payment.status != 'created':
                raise HttpError(400, "Payment record is not in created status")

            # 创建Antom会话请求
            alipay_create_session_request = AlipayCreateSessionRequest()
            alipay_create_session_request.product_code = ProductCodeType.CASHIER_PAYMENT
            alipay_create_session_request.product_scene = "CHECKOUT_PAYMENT"

            # 设置金额
            amount = Amount(payment.currency, payment.amount_in_cents)
            alipay_create_session_request.payment_amount = amount

            # 使用本地支付记录ID作为支付请求ID
            alipay_create_session_request.payment_request_id = str(payment.id)

            # 设置买家信息
            buyer = Buyer()
            buyer.reference_buyer_id = str(request.auth.id)

            # 设置商品信息
            goods = Goods()
            goods.goods_brand = "Z-Aiden"
            goods.goods_category = "subscription"
            goods.goods_name = f"{payment.membership_tier.title()} Subscription"
            goods.goods_quantity = "1"
            goods.goods_sku_name = payment.membership_tier
            goods.goods_unit_amount = amount
            goods.reference_goods_id = payment.membership_tier

            # 设置订单信息
            order = Order()
            order.reference_order_id = str(payment.id)
            order.order_description = f"Z-Aiden {payment.membership_tier} subscription"
            order.order_amount = amount
            order.buyer = buyer
            order.goods = goods
            alipay_create_session_request.order = order

            # 设置回调和重定向URL
            alipay_create_session_request.payment_notify_url = settings.ANTOM_NOTIFY_URL
            alipay_create_session_request.payment_redirect_url = f"{settings.ANTOM_REDIRECT_URL}?paymentId={payment.id}"

            # 调用Antom API
            start_time = time.time()
            alipay_create_session_response_body = self.default_alipay_client.execute(alipay_create_session_request)
            print(f"Antom session creation took: {time.time() - start_time} seconds")

            # 解析响应
            session_data = json.loads(alipay_create_session_response_body)

            # 更新支付记录
            with transaction.atomic():
                payment.antom_payment_request_id = str(payment.id)
                payment.status = 'pending'
                payment.metadata.update({
                    'antom_session_data': session_data,
                    'session_created_at': timezone.now().isoformat()
                })
                payment.save()

            return AntomSessionResponse(
                success=True,
                payment_id=str(payment.id),
                antom_payment_request_id=str(payment.id),
                session_data=session_data,
                redirect_url=session_data.get('paymentSessionData', {}).get('paymentSessionUrl')
            )

        except Payment.DoesNotExist:
            raise HttpError(404, "Payment record not found")
        except AlipayApiException as e:
            raise HttpError(500, f"Antom session creation failed: {str(e)}")
        except Exception as e:
            raise HttpError(500, f"Failed to create Antom session: {str(e)}")

    # receive notify
    @route.post("/receiveNotify", response=NotifyResponse)
    def receive_notify(self, request: HttpRequest):
        # retrieve the required parameters from http request
        notify_body = request.body.decode('utf-8')
        request_uri = request.path
        request_method = request.method

        # retrieve the required parameters from request header
        request_time = request.headers.get("request-time")
        client_id = request.headers.get("client-id")
        signature = request.headers.get("signature")

        try:
            # verify the signature of notification
            verify_result = check_signature(request_method, request_uri, client_id, request_time, str(notify_body),
                                            signature,
                                            settings.ANTOM_PUBLIC_KEY)
            if not verify_result:
                raise Exception("Invalid notify signature")

            # deserialize the notification body
            notify = json.loads(notify_body)
            if notify['notifyType'] == "PAYMENT_RESULT":
                alipay_pay_result_notify = AlipayPayResultNotify(notify_body)
                
                # 获取支付请求ID（应该是我们的本地支付记录ID）
                payment_request_id = notify.get('paymentRequestId')
                
                if alipay_pay_result_notify.result.result_code == "SUCCESS":
                    # 处理支付成功 - 更新本地 Payment 记录
                    try:
                        with transaction.atomic():
                            # 查找支付记录
                            payment = Payment.objects.get(id=payment_request_id)
                            
                            # 更新支付状态
                            payment.status = 'success'
                            payment.paid_at = timezone.now()
                            payment.metadata.update({
                                'antom_callback_data': notify,
                                'callback_received_at': timezone.now().isoformat()
                            })
                            payment.save()
                            
                            # 更新用户订阅
                            from .. payments.services import PaymentService
                            PaymentService.update_user_subscription(payment)
                            
                            print(f"Payment {payment_request_id} processed successfully")
                            
                    except Payment.DoesNotExist:
                        print(f"Payment record {payment_request_id} not found")
                    except Exception as e:
                        print(f"Error processing payment {payment_request_id}: {str(e)}")
                        
                    return jsonify({"result": {"resultCode": "SUCCESS", "resultMessage": "success.", "resultStatus": "S"}})
                
                else:
                    # 处理支付失败
                    try:
                        with transaction.atomic():
                            payment = Payment.objects.get(id=payment_request_id)
                            payment.status = 'failed'
                            payment.metadata.update({
                                'antom_callback_data': notify,
                                'callback_received_at': timezone.now().isoformat(),
                                'failure_reason': alipay_pay_result_notify.result.result_message
                            })
                            payment.save()
                            print(f"Payment {payment_request_id} failed: {alipay_pay_result_notify.result.result_message}")
                            
                    except Payment.DoesNotExist:
                        print(f"Payment record {payment_request_id} not found")
                    except Exception as e:
                        print(f"Error updating failed payment {payment_request_id}: {str(e)}")
                        
                    return jsonify({"result": {"resultCode": "SUCCESS", "resultMessage": "payment failed but notification processed.", "resultStatus": "S"}})

            if notify['notifyType'] == "CAPTURE_RESULT":
                alipay_capture_result_notify = AlipayCaptureResultNotify(notify_body)
                if alipay_capture_result_notify.result.result_code == "SUCCESS":
                    # handle your own business logic.
                    print(f"receive capture notify: {notify_body}")
                    return jsonify({"result": {"resultCode": "SUCCESS", "resultMessage": "success.", "resultStatus": "S"}})

        except Exception as e:
            print(str(e))
            return jsonify({"result": {"resultCode": "FAIL", "resultMessage": "fail.", "resultStatus": "F"}})

        return jsonify({"result": {"resultCode": "SYSTEM_ERROR", "resultMessage": "system error.", "resultStatus": "F"}})
