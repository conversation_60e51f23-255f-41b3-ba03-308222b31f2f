# This file now serves as a compatibility layer and imports from the new structure
# Import schemas from the separated modules

# Import from antom module
from .antom.schemas import (
    AntomSessionRequest, AntomSessionResponse, AntomCallbackRequest, AntomCallbackResponse,
    NotifyResult, NotifyResponse
)

# Import from payments module
from .payments.schemas import (
    PaymentCreateRequest, PaymentCreateResponse, PaymentStatusRequest, PaymentStatusResponse,
    PaymentHistoryResponse, ErrorResponse, SuccessResponse
)

# Re-export for backward compatibility
__all__ = [
    # Antom schemas
    'AntomSessionRequest', 'AntomSessionResponse', 'AntomCallbackRequest', 'AntomCallbackResponse',
    'NotifyResult', 'NotifyResponse',
    # Payment schemas
    'PaymentCreateRequest', 'PaymentCreateResponse', 'PaymentStatusRequest', 'PaymentStatusResponse',
    'PaymentHistoryResponse', 'ErrorResponse', 'SuccessResponse'
]
