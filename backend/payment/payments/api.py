from ninja_extra import api_controller, route
from ninja.errors import HttpError
from ninja_jwt.authentication import JWTAuth
from django.db import transaction
from django.utils import timezone
from decimal import Decimal

from ..models import Payment
from .schemas import (
    PaymentCreateRequest, PaymentCreateResponse, PaymentStatusResponse, PaymentHistoryResponse
)
from .services import PaymentService


@api_controller("/payment/payments", tags=["本地支付管理"])
class PaymentController:
    
    @route.post("/create", response=PaymentCreateResponse, auth=JWTAuth())
    def create_payment_record(self, request, data: PaymentCreateRequest):
        """创建支付记录"""
        try:
            with transaction.atomic():
                # 创建支付记录
                payment = Payment.objects.create(
                    user=request.auth,
                    amount=Decimal(str(data.amount)),
                    currency=data.currency,
                    membership_tier=data.membership_tier,
                    status='created'
                )

                return PaymentCreateResponse(
                    payment_id=str(payment.id),
                    amount=float(payment.amount),
                    currency=payment.currency,
                    membership_tier=payment.membership_tier,
                    status=payment.status,
                    created_at=payment.created_at
                )
        except Exception as e:
            raise HttpError(500, f"Failed to create payment record: {str(e)}")

    @route.get("/status/{payment_id}", response=PaymentStatusResponse, auth=JWTAuth())
    def get_payment_status(self, request, payment_id: str):
        """获取支付状态"""
        try:
            payment = Payment.objects.get(id=payment_id, user=request.auth)

            return PaymentStatusResponse(
                payment_id=str(payment.id),
                status=payment.status,
                amount=float(payment.amount),
                currency=payment.currency,
                membership_tier=payment.membership_tier,
                created_at=payment.created_at,
                updated_at=payment.updated_at,
                paid_at=payment.paid_at,
                antom_payment_request_id=payment.antom_payment_request_id
            )
        except Payment.DoesNotExist:
            raise HttpError(404, "Payment record not found")

    @route.get("/history", response=PaymentHistoryResponse, auth=JWTAuth())
    def get_payment_history(self, request, page: int = 1, page_size: int = 10):
        """获取支付历史"""
        try:
            payments = Payment.objects.filter(user=request.auth).order_by('-created_at')
            total = payments.count()

            start = (page - 1) * page_size
            end = start + page_size
            page_payments = payments[start:end]

            payment_list = [
                PaymentStatusResponse(
                    payment_id=str(p.id),
                    status=p.status,
                    amount=float(p.amount),
                    currency=p.currency,
                    membership_tier=p.membership_tier,
                    created_at=p.created_at,
                    updated_at=p.updated_at,
                    paid_at=p.paid_at,
                    antom_payment_request_id=p.antom_payment_request_id
                ) for p in page_payments
            ]

            return PaymentHistoryResponse(
                payments=payment_list,
                total=total,
                page=page,
                page_size=page_size
            )
        except Exception as e:
            raise HttpError(500, f"Failed to get payment history: {str(e)}")


