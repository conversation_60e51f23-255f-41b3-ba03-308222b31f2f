from pydantic import BaseModel, Field, field_validator
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid


# 支付创建相关模式
class PaymentCreateRequest(BaseModel):
    """创建支付请求"""
    amount: float = Field(..., gt=0, description="支付金额")
    currency: str = Field(default="CNY", description="货币类型")
    membership_tier: str = Field(..., description="会员等级")
    
    @field_validator('currency')
    def validate_currency(cls, v):
        allowed_currencies = ['CNY', 'HKD', 'USD']
        if v not in allowed_currencies:
            raise ValueError(f'Currency must be one of {allowed_currencies}')
        return v
    
    @field_validator('membership_tier')
    def validate_membership_tier(cls, v):
        allowed_tiers = ['free', 'premium', 'plus']
        if v not in allowed_tiers:
            raise ValueError(f'Membership tier must be one of {allowed_tiers}')
        return v


class PaymentCreateResponse(BaseModel):
    """创建支付响应"""
    payment_id: str = Field(..., description="本地支付记录ID")
    amount: float = Field(..., description="支付金额")
    currency: str = Field(..., description="货币类型")
    membership_tier: str = Field(..., description="会员等级")
    status: str = Field(..., description="支付状态")
    created_at: datetime = Field(..., description="创建时间")


# 支付状态查询相关模式
class PaymentStatusRequest(BaseModel):
    """支付状态查询请求"""
    payment_id: str = Field(..., description="支付记录ID")
    
    @field_validator('payment_id')
    def validate_payment_id(cls, v):
        try:
            uuid.UUID(v)
        except ValueError:
            raise ValueError('Invalid payment ID format')
        return v


class PaymentStatusResponse(BaseModel):
    """支付状态响应"""
    payment_id: str = Field(..., description="支付记录ID")
    status: str = Field(..., description="支付状态")
    amount: float = Field(..., description="支付金额")
    currency: str = Field(..., description="货币类型")
    membership_tier: str = Field(..., description="会员等级")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    paid_at: Optional[datetime] = Field(None, description="支付时间")
    antom_payment_request_id: Optional[str] = Field(None, description="Antom支付请求ID")


# 支付历史相关模式
class PaymentHistoryResponse(BaseModel):
    """支付历史响应"""
    payments: List[PaymentStatusResponse] = Field(..., description="支付记录列表")
    total: int = Field(..., description="总记录数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")



# 错误响应模式
class ErrorResponse(BaseModel):
    """错误响应"""
    error: str = Field(..., description="错误信息")
    code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


# 通用响应模式
class SuccessResponse(BaseModel):
    """成功响应"""
    success: bool = Field(True, description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
