from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import Payment


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'user_link', 'amount', 'currency', 'membership_tier',
        'status_badge', 'created_at', 'paid_at'
    ]
    list_filter = [
        'status', 'currency', 'membership_tier', 'created_at', 'paid_at'
    ]
    search_fields = [
        'id', 'user__username', 'user__email', 'antom_payment_request_id'
    ]
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'amount_in_cents',
        'subscription_dates_info', 'metadata_display'
    ]
    fieldsets = (
        ('基本信息', {
            'fields': ('id', 'user', 'amount', 'currency', 'membership_tier')
        }),
        ('状态信息', {
            'fields': ('status', 'created_at', 'updated_at', 'paid_at')
        }),
        ('Antom 信息', {
            'fields': ('antom_payment_request_id', 'antom_session_id'),
            'classes': ('collapse',)
        }),
        ('订阅信息', {
            'fields': ('subscription_start_date', 'subscription_end_date', 'subscription_dates_info'),
            'classes': ('collapse',)
        }),
        ('元数据', {
            'fields': ('metadata_display',),
            'classes': ('collapse',)
        }),
    )

    def user_link(self, obj):
        """显示用户链接"""
        if obj.user:
            url = reverse('admin:main_user_change', args=[obj.user.pk])
            return format_html('<a href="{}">{}</a>', url, obj.user.username)
        return '-'
    user_link.short_description = '用户'

    def status_badge(self, obj):
        """显示状态徽章"""
        colors = {
            'created': '#6c757d',
            'pending': '#ffc107',
            'processing': '#17a2b8',
            'success': '#28a745',
            'failed': '#dc3545',
            'cancelled': '#6c757d',
            'refunded': '#fd7e14'
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = '状态'

    def subscription_dates_info(self, obj):
        """显示订阅日期信息"""
        if obj.is_successful:
            start_date, end_date = obj.calculate_subscription_dates()
            if start_date and end_date:
                return f"开始: {start_date.strftime('%Y-%m-%d %H:%M')}\n结束: {end_date.strftime('%Y-%m-%d %H:%M')}"
            elif start_date:
                return f"开始: {start_date.strftime('%Y-%m-%d %H:%M')}"
        return "未计算"
    subscription_dates_info.short_description = '订阅日期'

    def metadata_display(self, obj):
        """显示格式化的元数据"""
        if obj.metadata:
            import json
            return format_html('<pre>{}</pre>', json.dumps(obj.metadata, indent=2, ensure_ascii=False))
        return "无"
    metadata_display.short_description = '元数据'

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user')

    def has_add_permission(self, request):
        """禁止通过 admin 添加支付记录"""
        return False

    def has_delete_permission(self, request, obj=None):
        """只允许删除失败或取消的支付记录"""
        if obj and obj.status in ['failed', 'cancelled']:
            return True
        return False
