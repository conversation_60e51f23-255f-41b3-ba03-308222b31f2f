from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
import uuid


class Payment(models.Model):
    """支付记录模型"""

    PAYMENT_STATUS_CHOICES = [
        ('created', _('已创建')),
        ('pending', _('待支付')),
        ('processing', _('处理中')),
        ('success', _('支付成功')),
        ('failed', _('支付失败')),
        ('cancelled', _('已取消')),
        ('refunded', _('已退款')),
    ]

    MEMBERSHIP_TIER_CHOICES = [
        ('free', _('免费版')),
        ('premium', _('高级版')),
        ('plus', _('专业版')),
    ]

    CURRENCY_CHOICES = [
        ('CNY', _('人民币')),
        ('HKD', _('港币')),
        ('USD', _('美元')),
        ('EUR', _('欧元')),
        ('GBP', _('英镑')),
    ]

    # 基本信息
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name=_('用户')
    )

    # 支付信息
    amount = models.DecimalField(
        _('支付金额'),
        max_digits=10,
        decimal_places=2,
        help_text=_('支付金额（原始单位）')
    )
    currency = models.CharField(
        _('货币类型'),
        max_length=3,
        choices=CURRENCY_CHOICES,
        default='CNY'
    )
    membership_tier = models.CharField(
        _('会员等级'),
        max_length=10,
        choices=MEMBERSHIP_TIER_CHOICES
    )

    # 状态和时间
    status = models.CharField(
        _('支付状态'),
        max_length=20,
        choices=PAYMENT_STATUS_CHOICES,
        default='created'
    )
    created_at = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_at = models.DateTimeField(_('更新时间'), auto_now=True)
    paid_at = models.DateTimeField(_('支付时间'), null=True, blank=True)

    # Antom 相关信息
    antom_payment_request_id = models.CharField(
        _('Antom支付请求ID'),
        max_length=255,
        null=True,
        blank=True,
        help_text=_('Antom返回的支付请求ID')
    )
    antom_session_id = models.CharField(
        _('Antom会话ID'),
        max_length=255,
        null=True,
        blank=True
    )

    # 元数据
    metadata = models.JSONField(
        _('元数据'),
        default=dict,
        blank=True,
        help_text=_('存储额外的支付相关信息')
    )

    # 订阅相关
    subscription_start_date = models.DateTimeField(
        _('订阅开始时间'),
        null=True,
        blank=True
    )
    subscription_end_date = models.DateTimeField(
        _('订阅结束时间'),
        null=True,
        blank=True
    )

    class Meta:
        verbose_name = _('支付记录')
        verbose_name_plural = _('支付记录')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['antom_payment_request_id']),
            models.Index(fields=['created_at']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"Payment {self.id} - {self.user} - {self.amount} {self.currency} - {self.status}"

    def save(self, *args, **kwargs):
        # 如果状态变为成功且还没有设置支付时间，则设置支付时间
        if self.status == 'success' and not self.paid_at:
            self.paid_at = timezone.now()
        super().save(*args, **kwargs)

    @property
    def is_successful(self):
        """判断支付是否成功"""
        return self.status == 'success'

    @property
    def is_pending(self):
        """判断支付是否待处理"""
        return self.status in ['created', 'pending', 'processing']

    @property
    def amount_in_cents(self):
        """获取以分为单位的金额（用于Antom）"""
        return int(self.amount * 100)

    def calculate_subscription_dates(self):
        """计算订阅开始和结束时间"""
        if not self.is_successful:
            return None, None

        start_date = self.paid_at or timezone.now()

        # 根据会员等级计算订阅时长
        if self.membership_tier == 'premium':
            # 高级版：1个月
            from dateutil.relativedelta import relativedelta
            end_date = start_date + relativedelta(months=1)
        elif self.membership_tier == 'plus':
            # 专业版：1个月
            from dateutil.relativedelta import relativedelta
            end_date = start_date + relativedelta(months=1)
        else:
            # 免费版不需要结束时间
            end_date = None

        return start_date, end_date
